"""
print0.py - وحدة إنشاء تقرير الإحصائيات العامة للمؤسسة (محسّن)

هذا الملف مسؤول عن إنشاء تقارير PDF جميلة وجذابة للإحصائيات العامة للمؤسسة.
يتم استدعاؤه من ملف sub5_window.py عند الضغط على زر "طباعة التقرير".

الميزات الجديدة:
- تصميم أنيق بألوان زاهية ومتدرجة
- تأثيرات بصرية ثلاثية الأبعاد
- ظلال متدرجة وحدود ذهبية
- بطاقات إحصائية جذابة
- تذييل متطور

تاريخ الإنشاء: 2023-10-01
آخر تحديث: 2024-01-15 (تحسينات التصميم)
"""

import os
import sqlite3
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.colors import HexColor
import arabic_reshaper
from bidi.algorithm import get_display

class StatisticsReport:
    """فئة مسؤولة عن إنشاء تقرير الإحصائيات العامة للمؤسسة"""

    def __init__(self, db_path="data.db"):
        """تهيئة الفئة مع مسار قاعدة البيانات"""
        self.db_path = db_path

    def get_academic_year(self):
        """استخراج السنة الدراسية الحالية من جدول بيانات_المؤسسة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استخراج السنة الدراسية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                return result[0]
            return ""

        except Exception as e:
            print(f"خطأ في استخراج السنة الدراسية: {e}")
            return ""

    def get_statistics(self, academic_year=""):
        """استخراج البيانات الإحصائية من قاعدة البيانات"""
        stats = {
            "levels": 0,
            "sections": 0,
            "students": 0,
            "males": 0,
            "females": 0,
            "age_groups": {}  # سيتم تخزين كل عمر كمفتاح مع عدد التلاميذ كقيمة
        }

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استخراج الإحصائيات العامة
            year_condition = " WHERE السنة_الدراسية = ?" if academic_year else ""
            params = [academic_year] if academic_year else []

            # استعلامات الإحصائيات الأساسية
            cursor.execute(f"SELECT COUNT(DISTINCT المستوى) FROM البنية_التربوية{year_condition}", params)
            stats["levels"] = cursor.fetchone()[0] or 0

            cursor.execute(f"SELECT COUNT(DISTINCT القسم) FROM البنية_التربوية{year_condition}", params)
            stats["sections"] = cursor.fetchone()[0] or 0

            cursor.execute(f"SELECT SUM(مجموع_التلاميذ) FROM البنية_التربوية{year_condition}", params)
            stats["students"] = cursor.fetchone()[0] or 0

            # استعلام النوع
            try:
                gender_query = """
                    SELECT النوع, COUNT(*)
                    FROM السجل_العام s
                    JOIN اللوائح l ON s.الرمز = l.الرمز
                """
                if academic_year:
                    gender_query += " WHERE l.السنة_الدراسية = ?"
                    gender_query += " GROUP BY النوع"
                    cursor.execute(gender_query, [academic_year])
                else:
                    gender_query += " GROUP BY النوع"
                    cursor.execute(gender_query)

                gender_stats = cursor.fetchall()
                for gender, count in gender_stats:
                    if gender and gender.strip().lower() in ['ذكر', 'ذ', 'm', 'male']:
                        stats["males"] += count
                    elif gender and gender.strip().lower() in ['أنثى', 'ا', 'f', 'female']:
                        stats["females"] += count

            except sqlite3.Error as e:
                print(f"خطأ في استرجاع إحصائيات النوع: {e}")

            conn.close()

        except Exception as e:
            print(f"خطأ في استرجاع الإحصائيات: {e}")

        return stats

    def calculate_age_stats(self, academic_year, reference_date):
        """حساب إحصائيات الأعمار بناءً على تاريخ مرجعي محدد"""
        age_stats = {}  # قاموس لتخزين كل عمر مع عدد التلاميذ

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            age_query = """
                SELECT تاريخ_الازدياد
                FROM السجل_العام s
                JOIN اللوائح l ON s.الرمز = l.الرمز
                WHERE l.السنة_الدراسية = ?
            """

            cursor.execute(age_query, [academic_year])
            birth_dates = cursor.fetchall()

            for (birth_date,) in birth_dates:
                try:
                    if isinstance(birth_date, str):
                        birth_date = birth_date.strip()

                        if '/' in birth_date:
                            day, month, year = map(int, birth_date.split('/'))
                        elif '-' in birth_date:
                            year, month, day = map(int, birth_date.split('-'))
                        else:
                            year = int(birth_date)
                            month, day = 1, 1

                        if year < 100:
                            year += 2000 if year < 30 else 1900

                        try:
                            birth_date = datetime(year, month, day)
                        except ValueError as e:
                            if "day is out of range for month" in str(e):
                                if month == 2:
                                    day = 29 if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0) else 28
                                elif month in [4, 6, 9, 11]:
                                    day = 30
                                else:
                                    day = 31
                                birth_date = datetime(year, month, day)
                            else:
                                raise
                    else:
                        birth_date = datetime(int(birth_date), 1, 1)

                    # حساب العمر
                    age = reference_date.year - birth_date.year
                    if reference_date.month < birth_date.month or \
                       (reference_date.month == birth_date.month and \
                        reference_date.day < birth_date.day):
                        age -= 1

                    # إضافة العمر إلى القاموس مع زيادة العداد
                    age_stats[age] = age_stats.get(age, 0) + 1

                except Exception as e:
                    print(f"خطأ في معالجة تاريخ الميلاد: {birth_date}, {str(e)}")
                    continue

            conn.close()

        except Exception as e:
            print(f"خطأ في حساب إحصائيات الأعمار: {str(e)}")

        return age_stats

    def get_school_info(self):
        """استخراج بيانات المؤسسة من قاعدة البيانات"""
        school_info = {}
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
            columns = [column[0] for column in cursor.description]
            result = cursor.fetchone()

            if result:
                school_info = {columns[i]: result[i] for i in range(len(columns))}

                # إضافة مسار الشعار إذا كان موجوداً
                school_info["logo_path"] = school_info.get("ImagePath1", "")

            conn.close()

        except Exception as e:
            print(f"خطأ في استخراج بيانات المؤسسة: {e}")

        return school_info

    def create_pdf_report(self, filepath, stats_data, reference_date):
        """إنشاء ملف PDF للتقرير بتصميم أنيق وجذاب بألوان زاهية

        المعلمات:
            filepath (str): مسار ملف PDF الذي سيتم إنشاؤه
            stats_data (dict): بيانات الإحصائيات التي سيتم عرضها في التقرير
            reference_date (datetime): تاريخ المرجع المستخدم لحساب أعمار التلاميذ

        العائد:
            bool: True إذا تم إنشاء التقرير بنجاح، False في حالة حدوث خطأ
        """
        try:
            # محاولة تسجيل الخط العربي من عدة مصادر
            font_paths = [
                os.path.join(os.path.dirname(__file__), "fonts", "arial.ttf"),
                r"C:\Windows\Fonts\arial.ttf",
                r"C:\Windows\Fonts\arialbd.ttf",
                r"C:\Windows\Fonts\calibri.ttf",
                r"C:\Windows\Fonts\tahoma.ttf"
            ]

            font_registered = False
            for font_path in font_paths:
                try:
                    if os.path.exists(font_path):
                        pdfmetrics.registerFont(TTFont("Arabic", font_path))
                        font_registered = True
                        break
                except:
                    continue

            # إنشاء ملف PDF
            c = canvas.Canvas(filepath, pagesize=A4)
            width, height = A4

            def reshape_arabic(text):
                return get_display(arabic_reshaper.reshape(str(text)))

            # استخراج البيانات
            school_info = self.get_school_info()

            # تعيين الخط المناسب
            font_name = "Arabic" if font_registered else "Helvetica"

            # خلفية متدرجة جميلة من الأزرق الفاتح إلى الأبيض
            # طبقة خلفية متدرجة
            for i in range(int(height)):
                alpha = i / height
                # تدرج من الأزرق الفاتح جداً إلى الأبيض
                blue_component = int(240 + (255 - 240) * alpha)
                green_component = int(248 + (255 - 248) * alpha)
                red_component = int(255)
                color = f"#{red_component:02x}{green_component:02x}{blue_component:02x}"
                c.setFillColor(HexColor(color))
                c.rect(0, i, width, 1, fill=True, stroke=False)

            # رأس التقرير بتصميم متدرج جذاب
            # ظل عميق للرأس (طبقة أولى)
            c.setFillColor(HexColor('#1a1a1a'))  # ظل أسود شفاف
            c.roundRect(45, height - 145, width - 85, 110, 25, fill=True, stroke=False)

            # ظل متوسط (طبقة ثانية)
            c.setFillColor(HexColor('#4a4a4a'))  # ظل رمادي متوسط
            c.roundRect(47, height - 143, width - 89, 108, 24, fill=True, stroke=False)

            # خلفية متدرجة للرأس - من البنفسجي إلى الأزرق
            header_height = 105
            for i in range(header_height):
                progress = i / header_height
                # تدرج من البنفسجي الجميل إلى الأزرق الزاهي
                red = int(138 - (138 - 67) * progress)    # من 138 إلى 67
                green = int(43 + (110 - 43) * progress)   # من 43 إلى 110
                blue = int(226 + (234 - 226) * progress)  # من 226 إلى 234
                color = f"#{red:02x}{green:02x}{blue:02x}"
                c.setFillColor(HexColor(color))
                c.rect(50, height - 140 + header_height - i - 1, width - 100, 1, fill=True, stroke=False)

            # حدود ذهبية لامعة للرأس
            c.setStrokeColor(HexColor('#FFD700'))  # ذهبي لامع
            c.setLineWidth(3)
            c.roundRect(50, height - 140, width - 100, 105, 22, fill=False, stroke=True)

            # حدود داخلية بيضاء للتأثير
            c.setStrokeColor(HexColor('#FFFFFF'))  # أبيض
            c.setLineWidth(1)
            c.roundRect(53, height - 137, width - 106, 99, 20, fill=False, stroke=True)

            # العنوان الرئيسي بتأثير ثلاثي الأبعاد
            # ظل النص (أولاً)
            c.setFillColor(HexColor('#000000'))  # أسود للظل
            c.setFont(font_name, 32)
            title_text = "📊 الإحصائيات العامة للمؤسسة"
            c.drawCentredString(width/2 + 2, height - 72, reshape_arabic(title_text))

            # النص الأساسي بلون أبيض لامع
            c.setFillColor(HexColor('#FFFFFF'))  # أبيض لامع
            c.drawCentredString(width/2, height - 70, reshape_arabic(title_text))

            # معلومات السنة الدراسية بتصميم جذاب
            c.setFillColor(HexColor('#FFE4B5'))  # بيج ذهبي فاتح
            c.setFont(font_name, 18)
            academic_year = school_info.get('السنة_الدراسية', 'غير محدد')
            year_text = f"🗓️ السنة الدراسية: {academic_year}"
            c.drawCentredString(width/2, height - 95, reshape_arabic(year_text))

            # اسم المؤسسة بخط أنيق
            c.setFillColor(HexColor('#F0F8FF'))  # أزرق فاتح جداً
            c.setFont(font_name, 16)
            school_name = school_info.get('المؤسسة', 'مؤسسة التعليم')
            school_text = f"🏫 {school_name}"
            c.drawCentredString(width/2, height - 118, reshape_arabic(school_text))

            # قسم الإحصائيات العامة بتصميم جذاب
            y = height - 200

            # عنوان القسم الأول بتأثير ثلاثي الأبعاد
            # ظل العنوان
            c.setFillColor(HexColor('#2F4F4F'))  # رمادي غامق للظل
            c.setFont(font_name, 26)
            section_title = "📈 الإحصائيات العامة"
            c.drawCentredString(width/2 + 1, y - 1, reshape_arabic(section_title))

            # العنوان الأساسي بلون زاهي
            c.setFillColor(HexColor('#FF6B6B'))  # أحمر زاهي جميل
            c.drawCentredString(width/2, y, reshape_arabic(section_title))

            # البطاقات الإحصائية بألوان زاهية متنوعة
            y -= 60
            card_data = [
                {
                    "icon": "👥",
                    "value": str(stats_data.get("students", 0)),
                    "label": "إجمالي التلاميذ",
                    "description": "العدد الكلي للطلاب المسجلين",
                    "color": "#FF6B6B",      # أحمر زاهي
                    "bg_gradient": ["#FFE5E5", "#FF6B6B"]  # تدرج أحمر
                },
                {
                    "icon": "🏫",
                    "value": str(stats_data.get("sections", 0)),
                    "label": "عدد الأقسام",
                    "description": "الأقسام المختلفة في المؤسسة",
                    "color": "#4ECDC4",      # تركوازي زاهي
                    "bg_gradient": ["#E5F9F8", "#4ECDC4"]  # تدرج تركوازي
                },
                {
                    "icon": "📚",
                    "value": str(stats_data.get("levels", 0)),
                    "label": "عدد المستويات",
                    "description": "المستويات التعليمية المتاحة",
                    "color": "#45B7D1",      # أزرق زاهي
                    "bg_gradient": ["#E5F4FD", "#45B7D1"]  # تدرج أزرق
                },
                {
                    "icon": "📊",
                    "value": str(int(stats_data.get("students", 0) / stats_data.get("sections", 1)) if stats_data.get("sections", 0) > 0 else 0),
                    "label": "معدل التلاميذ/قسم",
                    "description": "متوسط عدد الطلاب في كل قسم",
                    "color": "#96CEB4",      # أخضر زاهي
                    "bg_gradient": ["#E8F5F0", "#96CEB4"]  # تدرج أخضر
                }
            ]

            # رسم البطاقات في شبكة 2x2 بتصميم جذاب ومتطور
            cards_per_row = 2
            card_width = (width - 140) / cards_per_row
            card_height = 120  # أكبر قليلاً للتصميم الجديد

            for i, card in enumerate(card_data):
                row = i // cards_per_row
                col = i % cards_per_row

                x = 70 + col * (card_width + 30)
                card_y = y - (row * (card_height + 20))

                # ظل متدرج عميق (طبقات متعددة للعمق)
                for shadow_offset in range(8, 0, -1):
                    alpha = (8 - shadow_offset) / 8.0
                    shadow_color = int(50 + (200 - 50) * alpha)
                    c.setFillColor(HexColor(f'#{shadow_color:02x}{shadow_color:02x}{shadow_color:02x}'))
                    c.roundRect(x + shadow_offset, card_y - card_height - shadow_offset,
                              card_width, card_height, 25, fill=True, stroke=False)

                # خلفية البطاقة بتدرج جميل
                gradient_steps = 30
                for step in range(gradient_steps):
                    progress = step / gradient_steps
                    # تدرج من اللون الفاتح إلى اللون الأساسي
                    start_color = card["bg_gradient"][0].lstrip('#')
                    end_color = card["bg_gradient"][1].lstrip('#')

                    start_r, start_g, start_b = int(start_color[0:2], 16), int(start_color[2:4], 16), int(start_color[4:6], 16)
                    end_r, end_g, end_b = int(end_color[0:2], 16), int(end_color[2:4], 16), int(end_color[4:6], 16)

                    r = int(start_r + (end_r - start_r) * progress)
                    g = int(start_g + (end_g - start_g) * progress)
                    b = int(start_b + (end_b - start_b) * progress)

                    step_height = card_height / gradient_steps
                    c.setFillColor(HexColor(f'#{r:02x}{g:02x}{b:02x}'))
                    c.rect(x, card_y - card_height + step * step_height, card_width, step_height, fill=True, stroke=False)

                # حدود ذهبية لامعة
                c.setStrokeColor(HexColor('#FFD700'))
                c.setLineWidth(2)
                c.roundRect(x, card_y - card_height, card_width, card_height, 25, fill=False, stroke=True)

                # حدود داخلية بيضاء للتأثير
                c.setStrokeColor(HexColor('#FFFFFF'))
                c.setLineWidth(1)
                c.roundRect(x + 2, card_y - card_height + 2, card_width - 4, card_height - 4, 23, fill=False, stroke=True)

                # شريط علوي متدرج
                stripe_height = 8
                for stripe_step in range(stripe_height):
                    stripe_progress = stripe_step / stripe_height
                    base_color = card["color"].lstrip('#')
                    r, g, b = int(base_color[0:2], 16), int(base_color[2:4], 16), int(base_color[4:6], 16)

                    # تدرج من اللون الأساسي إلى أبيض
                    new_r = int(r + (255 - r) * stripe_progress)
                    new_g = int(g + (255 - g) * stripe_progress)
                    new_b = int(b + (255 - b) * stripe_progress)

                    c.setFillColor(HexColor(f'#{new_r:02x}{new_g:02x}{new_b:02x}'))
                    c.rect(x, card_y - stripe_step - 1, card_width, 1, fill=True, stroke=False)

                # الأيقونة مع ظل
                # ظل الأيقونة
                c.setFillColor(HexColor('#000000'))
                c.setFont(font_name, 36)
                c.drawCentredString(x + card_width/2 + 1, card_y - 41, card["icon"])

                # الأيقونة الأساسية
                c.setFillColor(HexColor('#FFFFFF'))
                c.drawCentredString(x + card_width/2, card_y - 40, card["icon"])

                # القيمة الرقمية بتأثير ثلاثي الأبعاد
                # ظل القيمة
                c.setFillColor(HexColor('#000000'))
                c.setFont(font_name, 36)
                c.drawCentredString(x + card_width/2 + 2, card_y - 72, card["value"])

                # القيمة الأساسية
                c.setFillColor(HexColor(card["color"]))
                c.drawCentredString(x + card_width/2, card_y - 70, card["value"])

                # العنوان بخط أنيق
                c.setFillColor(HexColor('#2C3E50'))  # أزرق غامق أنيق
                c.setFont(font_name, 16)
                c.drawCentredString(x + card_width/2, card_y - 95, reshape_arabic(card["label"]))

                # الوصف بلون متناسق
                c.setFillColor(HexColor('#7F8C8D'))  # رمادي أنيق
                c.setFont(font_name, 11)
                c.drawCentredString(x + card_width/2, card_y - 115, reshape_arabic(card["description"]))

            # قسم إحصائيات النوع بتصميم جذاب
            y -= 280  # مساحة كافية للبطاقات الجديدة

            # عنوان القسم الثاني بتأثير ثلاثي الأبعاد
            # ظل العنوان
            c.setFillColor(HexColor('#2F4F4F'))
            c.setFont(font_name, 26)
            gender_title = "👫 الإحصائيات حسب النوع"
            c.drawCentredString(width/2 + 1, y - 1, reshape_arabic(gender_title))

            # العنوان الأساسي بلون بنفسجي زاهي
            c.setFillColor(HexColor('#9B59B6'))  # بنفسجي زاهي
            c.drawCentredString(width/2, y, reshape_arabic(gender_title))

            # حساب الإحصائيات
            males = stats_data.get("males", 0)
            females = stats_data.get("females", 0)
            total_gender = males + females
            female_percentage = round((females / total_gender) * 100, 1) if total_gender > 0 else 0

            # بطاقات النوع بألوان زاهية ومتنوعة
            y -= 60
            gender_cards = [
                {
                    "icon": "👦",
                    "value": str(males),
                    "label": "عدد الذكور",
                    "description": "الطلاب الذكور",
                    "color": "#3498DB",      # أزرق زاهي للذكور
                    "bg_gradient": ["#EBF3FD", "#3498DB"]
                },
                {
                    "icon": "👧",
                    "value": str(females),
                    "label": "عدد الإناث",
                    "description": "الطالبات الإناث",
                    "color": "#E91E63",      # وردي زاهي للإناث
                    "bg_gradient": ["#FCE4EC", "#E91E63"]
                },
                {
                    "icon": "👥",
                    "value": str(total_gender),
                    "label": "الإجمالي",
                    "description": "المجموع الكلي",
                    "color": "#9B59B6",      # بنفسجي زاهي
                    "bg_gradient": ["#F4ECF7", "#9B59B6"]
                },
                {
                    "icon": "📊",
                    "value": f"{female_percentage}%",
                    "label": "نسبة الإناث",
                    "description": "النسبة المئوية للإناث",
                    "color": "#F39C12",      # برتقالي زاهي
                    "bg_gradient": ["#FEF9E7", "#F39C12"]
                }
            ]

            # رسم بطاقات النوع بنفس التصميم الجذاب
            for i, card in enumerate(gender_cards):
                row = i // cards_per_row
                col = i % cards_per_row

                x = 70 + col * (card_width + 30)
                card_y = y - (row * (card_height + 20))

                # ظل متدرج عميق (طبقات متعددة للعمق)
                for shadow_offset in range(8, 0, -1):
                    alpha = (8 - shadow_offset) / 8.0
                    shadow_color = int(50 + (200 - 50) * alpha)
                    c.setFillColor(HexColor(f'#{shadow_color:02x}{shadow_color:02x}{shadow_color:02x}'))
                    c.roundRect(x + shadow_offset, card_y - card_height - shadow_offset,
                              card_width, card_height, 25, fill=True, stroke=False)

                # خلفية البطاقة بتدرج جميل
                gradient_steps = 30
                for step in range(gradient_steps):
                    progress = step / gradient_steps
                    start_color = card["bg_gradient"][0].lstrip('#')
                    end_color = card["bg_gradient"][1].lstrip('#')

                    start_r, start_g, start_b = int(start_color[0:2], 16), int(start_color[2:4], 16), int(start_color[4:6], 16)
                    end_r, end_g, end_b = int(end_color[0:2], 16), int(end_color[2:4], 16), int(end_color[4:6], 16)

                    r = int(start_r + (end_r - start_r) * progress)
                    g = int(start_g + (end_g - start_g) * progress)
                    b = int(start_b + (end_b - start_b) * progress)

                    step_height = card_height / gradient_steps
                    c.setFillColor(HexColor(f'#{r:02x}{g:02x}{b:02x}'))
                    c.rect(x, card_y - card_height + step * step_height, card_width, step_height, fill=True, stroke=False)

                # حدود ذهبية لامعة
                c.setStrokeColor(HexColor('#FFD700'))
                c.setLineWidth(2)
                c.roundRect(x, card_y - card_height, card_width, card_height, 25, fill=False, stroke=True)

                # حدود داخلية بيضاء
                c.setStrokeColor(HexColor('#FFFFFF'))
                c.setLineWidth(1)
                c.roundRect(x + 2, card_y - card_height + 2, card_width - 4, card_height - 4, 23, fill=False, stroke=True)

                # شريط علوي متدرج
                stripe_height = 8
                for stripe_step in range(stripe_height):
                    stripe_progress = stripe_step / stripe_height
                    base_color = card["color"].lstrip('#')
                    r, g, b = int(base_color[0:2], 16), int(base_color[2:4], 16), int(base_color[4:6], 16)

                    new_r = int(r + (255 - r) * stripe_progress)
                    new_g = int(g + (255 - g) * stripe_progress)
                    new_b = int(b + (255 - b) * stripe_progress)

                    c.setFillColor(HexColor(f'#{new_r:02x}{new_g:02x}{new_b:02x}'))
                    c.rect(x, card_y - stripe_step - 1, card_width, 1, fill=True, stroke=False)

                # الأيقونة مع ظل
                c.setFillColor(HexColor('#000000'))
                c.setFont(font_name, 36)
                c.drawCentredString(x + card_width/2 + 1, card_y - 41, card["icon"])

                c.setFillColor(HexColor('#FFFFFF'))
                c.drawCentredString(x + card_width/2, card_y - 40, card["icon"])

                # القيمة الرقمية بتأثير ثلاثي الأبعاد
                c.setFillColor(HexColor('#000000'))
                c.setFont(font_name, 36)
                c.drawCentredString(x + card_width/2 + 2, card_y - 72, card["value"])

                c.setFillColor(HexColor(card["color"]))
                c.drawCentredString(x + card_width/2, card_y - 70, card["value"])

                # العنوان والوصف
                c.setFillColor(HexColor('#2C3E50'))
                c.setFont(font_name, 16)
                c.drawCentredString(x + card_width/2, card_y - 95, reshape_arabic(card["label"]))

                c.setFillColor(HexColor('#7F8C8D'))
                c.setFont(font_name, 11)
                c.drawCentredString(x + card_width/2, card_y - 115, reshape_arabic(card["description"]))

            # تذييل جميل ومتطور
            y = 120

            # ظل متدرج للتذييل
            for shadow_offset in range(6, 0, -1):
                alpha = (6 - shadow_offset) / 6.0
                shadow_color = int(100 + (150 - 100) * alpha)
                c.setFillColor(HexColor(f'#{shadow_color:02x}{shadow_color:02x}{shadow_color:02x}'))
                c.roundRect(60 + shadow_offset, y - 65 - shadow_offset, width - 120, 60, 20, fill=True, stroke=False)

            # خلفية التذييل بتدرج جميل
            footer_height = 60
            for step in range(footer_height):
                progress = step / footer_height
                # تدرج من الأزرق الغامق إلى البنفسجي
                r = int(52 + (155 - 52) * progress)    # من 52 إلى 155
                g = int(73 + (89 - 73) * progress)     # من 73 إلى 89
                b = int(94 + (182 - 94) * progress)    # من 94 إلى 182

                c.setFillColor(HexColor(f'#{r:02x}{g:02x}{b:02x}'))
                c.rect(60, y - 65 + step, width - 120, 1, fill=True, stroke=False)

            # حدود ذهبية للتذييل
            c.setStrokeColor(HexColor('#FFD700'))
            c.setLineWidth(2)
            c.roundRect(60, y - 65, width - 120, 60, 20, fill=False, stroke=True)

            # حدود داخلية بيضاء
            c.setStrokeColor(HexColor('#FFFFFF'))
            c.setLineWidth(1)
            c.roundRect(62, y - 63, width - 124, 56, 18, fill=False, stroke=True)

            # أيقونة التقرير
            c.setFillColor(HexColor('#FFFFFF'))
            c.setFont(font_name, 24)
            c.drawCentredString(width/2, y - 25, "📋")

            # معلومات التقرير بتأثير ثلاثي الأبعاد
            # ظل النص
            c.setFillColor(HexColor('#000000'))
            c.setFont(font_name, 16)
            report_info = f"تم إنشاء التقرير في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            c.drawCentredString(width/2 + 1, y - 46, reshape_arabic(report_info))

            # النص الأساسي
            c.setFillColor(HexColor('#FFFFFF'))
            c.drawCentredString(width/2, y - 45, reshape_arabic(report_info))

            # خط فاصل زخرفي في الأسفل
            c.setStrokeColor(HexColor('#FFD700'))
            c.setLineWidth(3)
            c.line(80, 30, width - 80, 30)

            # نقاط زخرفية
            for i in range(5):
                x_pos = 100 + i * ((width - 200) / 4)
                c.setFillColor(HexColor('#FFD700'))
                c.circle(x_pos, 30, 3, fill=True, stroke=False)

            # حفظ الملف
            c.save()
            return True

        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {e}")
            return False
