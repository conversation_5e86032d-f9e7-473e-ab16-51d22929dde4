# تشخيص عميق لمشكلة تحديث السجل_العام في sub4_window.py

## 🔍 **التشخيص المضاف:**

لقد أضفت تشخيص مفصل لكشف السبب الجذري لعدم تحديث جدول `السجل_العام`:

### **1. فحص بيانات المؤسسة:**
```python
# فحص بيانات المؤسسة
institution_query = QSqlQuery(db=self.db)
if institution_query.exec_("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1"):
    if institution_query.next():
        inst_year = institution_query.value(0)
        inst_semester = institution_query.value(1)
        print(f"🏫 بيانات المؤسسة - السنة: {inst_year}, الأسدس: {inst_semester}")
    else:
        print("⚠️ لا توجد بيانات في جدول بيانات_المؤسسة!")
```

### **2. فحص وجود التلميذ في السجل_العام:**
```python
# التحقق من وجود التلميذ في السجل_العام قبل التحديث
check_query = QSqlQuery(db=self.db)
check_query.prepare("SELECT الرمز, السماح FROM السجل_العام WHERE الرمز = ?")
check_query.bindValue(0, student['code'])

if check_query.exec_() and check_query.next():
    old_count = check_query.value(1)
    print(f"📊 العدد الحالي للسماح في السجل_العام: {old_count}")
else:
    print(f"⚠️ التلميذ {student['code']} غير موجود في السجل_العام!")
```

### **3. اختبار الاستعلام الفرعي:**
```python
# اختبار الاستعلام الفرعي أولاً
test_query = QSqlQuery(db=self.db)
test_sql = """
    SELECT COUNT(*)
    FROM ورقة_السماح_بالدخول w
    WHERE w.الرمز = ?
    AND w.سماح = 1
    AND w.السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
    AND w.الأسدس = (SELECT الأسدس FROM بيانات_المؤسسة LIMIT 1)
"""
test_query.prepare(test_sql)
test_query.bindValue(0, student['code'])

if test_query.exec_() and test_query.next():
    count_result = test_query.value(0)
    print(f"🧪 نتيجة الاستعلام الفرعي: {count_result}")
```

### **4. فحص نتيجة التحديث:**
```python
if query_update.exec_():
    rows_affected = query_update.numRowsAffected()
    print(f"✅ تم تحديث السجل_العام - عدد الصفوف المتأثرة: {rows_affected}")
    
    # التحقق من النتيجة بعد التحديث
    if check_query.exec_() and check_query.next():
        new_count = check_query.value(1)
        print(f"📈 العدد الجديد للسماح في السجل_العام: {new_count}")
else:
    print(f"❌ خطأ في تحديث السجل_العام: {query_update.lastError().text()}")
```

---

## 🎯 **الأسباب المحتملة للمشكلة:**

### **1. مشكلة في بيانات المؤسسة:**
- جدول `بيانات_المؤسسة` فارغ
- السنة الدراسية أو الأسدس غير صحيحين
- عدم تطابق البيانات

### **2. مشكلة في السجل_العام:**
- التلميذ غير موجود في جدول `السجل_العام`
- مشكلة في بنية الجدول
- مشكلة في الأذونات

### **3. مشكلة في الاستعلام الفرعي:**
- الاستعلام الفرعي يعيد 0 دائماً
- مشكلة في شروط البحث
- مشكلة في ربط الجداول

### **4. مشكلة في التوقيت:**
- البيانات لم تُحفظ بعد في `ورقة_السماح_بالدخول`
- مشكلة في الـ transaction
- مشكلة في التزامن

---

## 🧪 **للاختبار الآن:**

1. **شغل البرنامج** وافتح نافذة `sub4_window.py`
2. **حدد تلميذ** واضغط على "ورقة الدخول"
3. **راقب الرسائل** في وحدة التحكم بعناية

### **الرسائل المتوقعة:**

```
🔍 بدء معالجة 1 تلميذ للسماح بالدخول
📅 السنة الدراسية: 2024/2025, الأسدس: الأول
🏫 بيانات المؤسسة - السنة: 2024/2025, الأسدس: الأول
🔍 استعلام التحديث:
UPDATE السجل_العام SET السماح = (SELECT COUNT(*) FROM ورقة_السماح_بالدخول w WHERE w.الرمز = :code AND w.سماح = 1 AND w.السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1) AND w.الأسدس = (SELECT الأسدس FROM بيانات_المؤسسة LIMIT 1)) WHERE الرمز = :code

👤 معالجة التلميذ: اسم التلميذ - الرمز: 12345
✅ تم إدراج السماح للتلميذ 12345 بنجاح
📊 العدد الحالي للسماح في السجل_العام: 0
🧪 نتيجة الاستعلام الفرعي: 1
✅ تم تحديث السجل_العام - عدد الصفوف المتأثرة: 1
📈 العدد الجديد للسماح في السجل_العام: 1
```

### **إذا ظهرت رسائل خطأ:**

- **⚠️ لا توجد بيانات في جدول بيانات_المؤسسة!** ← مشكلة في بيانات المؤسسة
- **⚠️ التلميذ غير موجود في السجل_العام!** ← مشكلة في السجل_العام
- **🧪 نتيجة الاستعلام الفرعي: 0** ← مشكلة في الاستعلام الفرعي
- **❌ خطأ في تحديث السجل_العام** ← مشكلة في استعلام التحديث

---

## 🎯 **الخطوة التالية:**

**جرب الآن وأخبرني بالرسائل التي تظهر بالضبط!**

هذا التشخيص المفصل سيكشف لنا السبب الجذري للمشكلة ونتمكن من إصلاحها نهائياً. 🔍🚀
