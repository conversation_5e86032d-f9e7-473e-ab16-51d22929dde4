import sys
import os
import sqlite3
import traceback
import shutil
import time
import webbrowser
import json
from datetime import datetime
from PyQt5.QtWidgets import (
    QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout, QFrame,
    QPushButton, QLabel, QComboBox, QDateEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QCheckBox, QDialog, QDialogButtonBox,
    QApplication, QStackedWidget, QTreeWidget, QTreeWidgetItem, QGridLayout,
    QScrollArea, QSizePolicy
)
from PyQt5.QtCore import Qt, QDate, pyqtSlot, QUrl, QObject
from PyQt5.QtGui import QFont
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel

# استيراد وحدة رسائل التأكيد المخصصة
from sub100_window import ConfirmationDialogs
# استيراد دالة إنشاء التقرير من print10.py
from print10 import generate_teacher_pdf_report_code, download_arabic_fonts, ArabicPDF
# استيراد دالة تقرير القسم من print100.py
from print100 import generate_section_pdf_report
# استيراد نافذة حذف الفروض الممسوكة
from sub210_window import show_delete_exams_dialog

# استيراد المكتبات اللازمة لإنشاء ملفات PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.pdfbase import pdfmetrics
    from reportlab.lib.units import cm
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT

    # استيراد دعم العربية
    import arabic_reshaper
    from bidi.algorithm import get_display

    REPORTLAB_AVAILABLE = True

    # محاولة تسجيل خط عربي
    try:
        fonts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")
        if not os.path.exists(fonts_dir):
            os.makedirs(fonts_dir)

        arabic_fonts = [
            "Arial.ttf", "arialbd.ttf", "Calibri.ttf", "calibrib.ttf",
            "Tahoma.ttf", "tahomabd.ttf", "Amiri-Regular.ttf", "Amiri-Bold.ttf"
        ]

        system_fonts_dir = os.path.join(os.environ.get('WINDIR', ''), 'Fonts')
        arabic_font_registered = False

        for font_name in arabic_fonts:
            local_font_path = os.path.join(fonts_dir, font_name)
            system_font_path = os.path.join(system_fonts_dir, font_name)

            if os.path.exists(local_font_path):
                pdfmetrics.registerFont(TTFont('Arabic', local_font_path))
                arabic_font_registered = True
                break
            elif os.path.exists(system_font_path):
                shutil.copy2(system_font_path, local_font_path)
                pdfmetrics.registerFont(TTFont('Arabic', local_font_path))
                arabic_font_registered = True
                break

        if not arabic_font_registered:
            print("لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
    except Exception as e:
        print(f"خطأ في تسجيل الخط العربي: {e}")

except ImportError as e:
    print(f"خطأ في استيراد مكتبات إنشاء التقارير: {e}")
    REPORTLAB_AVAILABLE = False

class WebInterface(QObject):
    """فئة لتوفير واجهة بين JavaScript و Python"""
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window

    @pyqtSlot(str, result=str)
    def loadSubjects(self, _):
        """تحميل المواد الدراسية"""
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT المادة FROM الأساتذة
                WHERE المادة IS NOT NULL AND المادة != ''
                ORDER BY المادة
            """)
            subjects = [row[0] for row in cursor.fetchall()]
            conn.close()
            return json.dumps(subjects, ensure_ascii=False)
        except Exception as e:
            print(f"خطأ في تحميل المواد: {e}")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(str, result=str)
    def loadTeachers(self, subject):
        """تحميل الأساتذة بناءً على المادة"""
        try:
            if not subject:
                return json.dumps([], ensure_ascii=False)
                
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT اسم_الأستاذ FROM الأساتذة
                WHERE المادة = ? AND اسم_الأستاذ IS NOT NULL AND اسم_الأستاذ != ''
                ORDER BY اسم_الأستاذ
            """, (subject,))
            teachers = [row[0] for row in cursor.fetchall()]
            conn.close()
            return json.dumps(teachers, ensure_ascii=False)
        except Exception as e:
            print(f"خطأ في تحميل الأساتذة: {e}")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(result=str)
    def loadLevels(self):
        """تحميل المستويات"""
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT المستوى FROM البنية_التربوية
                WHERE المستوى IS NOT NULL AND المستوى != ''
                ORDER BY ترتيب_المستويات, المستوى
            """)
            levels = [row[0] for row in cursor.fetchall()]
            conn.close()
            return json.dumps(levels, ensure_ascii=False)
        except Exception as e:
            print(f"خطأ في تحميل المستويات: {e}")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(str, result=str)
    def loadSections(self, level):
        """تحميل الأقسام بناءً على المستوى"""
        try:
            if not level:
                return json.dumps([], ensure_ascii=False)
                
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT القسم FROM البنية_التربوية
                WHERE المستوى = ? AND القسم IS NOT NULL AND القسم != ''
            """, (level,))
            sections_data = [row[0] for row in cursor.fetchall()]
            
            # ترتيب الأقسام
            def extract_section_number(section_name_arg):
                import re
                match = re.search(r'(\d+)[/\-]?(\d*)', section_name_arg)
                if match:
                    if match.group(2):
                        return int(match.group(1)) * 100 + int(match.group(2))
                    return int(match.group(1)) * 100
                return 9999
            
            sections_data.sort(key=extract_section_number)
            conn.close()
            return json.dumps(sections_data, ensure_ascii=False)
        except Exception as e:
            print(f"خطأ في تحميل الأقسام: {e}")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(str, str, str, str, str, result=str)
    def saveExams(self, date, subject, teacher, selected_sections_json, selected_exams_json):
        """حفظ الفروض"""
        try:
            selected_sections = json.loads(selected_sections_json)
            selected_exams = json.loads(selected_exams_json)
            
            if not subject or not teacher or not selected_sections or not selected_exams:
                return json.dumps({"success": False, "message": "يرجى إكمال جميع البيانات المطلوبة"}, ensure_ascii=False)
            
            # الحصول على السنة الدراسية والأسدس
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            current_year = result[0] if result else ""
            current_semester = result[1] if result else ""
            
            exams_added = 0
            absent_students = ""
            
            for section in selected_sections:
                # الحصول على المستوى الصحيح للقسم
                cursor.execute("SELECT المستوى FROM البنية_التربوية WHERE القسم = ? AND السنة_الدراسية = ? LIMIT 1", (section, current_year))
                level_result = cursor.fetchone()
                current_level_for_section = level_result[0] if level_result else "غير محدد"
                
                for exam_type in selected_exams:
                    cursor.execute("""
                        SELECT id FROM مسك_أوراق_الفروض
                        WHERE الأستاذ = ? AND القسم = ? AND نوع_الفرض = ? AND الأسدس = ? AND السنة_الدراسية = ?
                    """, (teacher, section, exam_type, current_semester, current_year))
                    existing_record = cursor.fetchone()

                    if existing_record:
                        cursor.execute("""
                            UPDATE مسك_أوراق_الفروض
                            SET التاريخ = ?, المادة = ?, المستوى = ?, المتغيبون = ?, تاريخ_التسجيل = ?
                            WHERE id = ?
                        """, (date, subject, current_level_for_section, absent_students, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), existing_record[0]))
                        exams_added += 1
                    else:
                        cursor.execute("""
                            INSERT INTO مسك_أوراق_الفروض (التاريخ, المادة, الأستاذ, المستوى, القسم, نوع_الفرض, المتغيبون, تاريخ_التسجيل, السنة_الدراسية, الأسدس)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (date, subject, teacher, current_level_for_section, section, exam_type, absent_students, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), current_year, current_semester))
                        exams_added += 1

            conn.commit()
            conn.close()

            return json.dumps({
                "success": True,
                "message": f"تم مسك {exams_added} فرض بنجاح",
                "details": {
                    "subject": subject,
                    "teacher": teacher,
                    "sections": selected_sections,
                    "exams": selected_exams,
                    "count": exams_added
                }
            }, ensure_ascii=False)

        except Exception as e:
            print(f"خطأ في حفظ الفروض: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "message": f"حدث خطأ: {str(e)}"}, ensure_ascii=False)

    @pyqtSlot()
    def showDeleteExamDialog(self):
        """عرض نافذة حذف الفروض"""
        show_delete_exams_dialog(self.main_window)

    @pyqtSlot()
    def showTeacherReport(self):
        """عرض تقرير الأستاذ"""
        self.main_window.show_teacher_exams_report()

    @pyqtSlot()
    def showSectionReport(self):
        """عرض تقرير القسم"""
        self.main_window.show_section_exams_report()

class CreateAbsenceTableWindowHTML(QMainWindow):
    """نافذة مسك أوراق الفروض باستخدام HTML/CSS/JavaScript"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("معالج مسك أوراق الفروض - HTML")
        self.setObjectName("CreateAbsenceTableWindowHTML")
        self.setFixedSize(1200, 800)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        self.setCentralWidget(self.web_view)

        # إنشاء قناة الويب للتواصل مع JavaScript
        self.web_channel = QWebChannel()
        self.web_interface = WebInterface(self)
        self.web_channel.registerObject("pyInterface", self.web_interface)
        self.web_view.page().setWebChannel(self.web_channel)

        # التأكد من وجود الجداول
        self.ensure_table_exists()

        # تحميل محتوى HTML
        self.load_html_content()

    def ensure_table_exists(self):
        """التأكد من وجود جدول مسك_أوراق_الفروض في قاعدة البيانات"""
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS مسك_أوراق_الفروض (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    التاريخ TEXT,
                    المادة TEXT,
                    الأستاذ TEXT,
                    المستوى TEXT,
                    القسم TEXT,
                    نوع_الفرض TEXT,
                    المتغيبون TEXT,
                    تاريخ_التسجيل TEXT,
                    السنة_الدراسية TEXT,
                    الأسدس TEXT
                )
            """)
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"خطأ في إنشاء الجدول: {e}")

    def load_html_content(self):
        """تحميل محتوى HTML"""
        html_content = self.get_html_content()
        self.web_view.setHtml(html_content)

    def get_html_content(self):
        """إنشاء محتوى HTML"""
        return """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معالج مسك أوراق الفروض</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', Arial, sans-serif;
            background: linear-gradient(135deg, #F5F7FA 0%, #C3CFE2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1150px;
            margin: 0 auto;
            padding: 20px;
        }

        .main-title {
            background: white;
            border: 2px solid #1976D2;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .main-title h1 {
            color: #1976D2;
            font-size: 24px;
            font-weight: bold;
        }

        .wizard-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            overflow: hidden;
            min-height: 500px;
        }

        .stage {
            display: none;
            padding: 30px;
            min-height: 500px;
        }

        .stage.active {
            display: block;
        }

        .stage-title {
            background: #1976D2;
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: bold;
        }

        .form-group {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0;
        }

        .form-label {
            background: white;
            border: 1.5px solid #1976D2;
            padding: 8px 15px;
            font-weight: bold;
            width: 150px;
            text-align: center;
            font-size: 14px;
            border-radius: 0;
        }

        .form-input {
            background: white;
            border: 1.5px solid #1976D2;
            padding: 8px 15px;
            width: 250px;
            font-size: 14px;
            font-weight: bold;
            border-radius: 0;
            direction: rtl;
        }

        .form-input:focus {
            outline: none;
            border-color: #0D47A1;
            box-shadow: 0 0 5px rgba(25, 118, 210, 0.3);
        }

        .level-navigator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .level-card {
            background: #E3F2FD;
            border: 2px solid #1976D2;
            border-radius: 8px;
            padding: 10px 30px;
            font-size: 16px;
            font-weight: bold;
            color: #1976D2;
            min-width: 200px;
            text-align: center;
        }

        .nav-button {
            background: #4682B4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .nav-button:hover {
            background: #5C94C5;
        }

        .nav-button:disabled {
            background: #CCCCCC;
            cursor: not-allowed;
        }

        .sections-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            max-height: 200px;
            overflow-y: auto;
            padding: 20px;
            border: 2px solid #1976D2;
            border-radius: 8px;
            background: #FAFAFA;
        }

        .section-card {
            background: white;
            border: 2px solid #CCCCCC;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .section-card:hover {
            border-color: #1976D2;
            transform: translateY(-2px);
        }

        .section-card.selected {
            background: #E3F2FD;
            border-color: #1976D2;
            color: #1976D2;
        }

        .exam-types-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .exam-types-table th,
        .exam-types-table td {
            border: 2px solid #1976D2;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }

        .exam-types-table th {
            background: #1976D2;
            color: white;
            font-size: 16px;
        }

        .exam-types-table td {
            background: white;
            font-size: 14px;
        }

        .exam-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .navigation-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }

        .nav-btn {
            background: #4682B4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s;
        }

        .nav-btn:hover {
            background: #5C94C5;
        }

        .nav-btn:disabled {
            background: #CCCCCC;
            cursor: not-allowed;
        }

        .action-buttons {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .action-btn {
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 13px;
            transition: all 0.3s;
        }

        .save-btn {
            background: #4CAF50;
        }

        .save-btn:hover {
            background: #388E3C;
        }

        .cancel-btn {
            background: #F44336;
        }

        .cancel-btn:hover {
            background: #D32F2F;
        }

        .delete-btn {
            background: #E53935;
        }

        .delete-btn:hover {
            background: #D32F2F;
        }

        .report-btn {
            background: #00897B;
        }

        .report-btn:hover {
            background: #00796B;
        }

        .teacher-report-btn {
            background: #039BE5;
        }

        .teacher-report-btn:hover {
            background: #0288D1;
        }

        .status-bar {
            background: #ECEFF1;
            border-top: 1px solid #CFD8DC;
            padding: 10px;
            text-align: center;
            color: #1976D2;
            font-weight: bold;
            margin-top: 20px;
        }

        .success-message {
            background: #E8F5E8;
            border: 2px solid #4CAF50;
            color: #2E7D32;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }

        .error-message {
            background: #FFEBEE;
            border: 2px solid #F44336;
            color: #C62828;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }

        .selected-items {
            background: #F3E5F5;
            border: 1px solid #9C27B0;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            max-height: 100px;
            overflow-y: auto;
        }

        .instruction-text {
            background: #E3F2FD;
            border: 1px solid #1976D2;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- العنوان الرئيسي -->
        <div class="main-title">
            <h1>معالج مسك أوراق الفروض</h1>
        </div>

        <!-- حاوي المعالج -->
        <div class="wizard-container">
            <!-- المرحلة الأولى: بيانات المادة والأستاذ -->
            <div id="stage1" class="stage active">
                <div class="stage-title">المرحلة 1: بيانات المادة والأستاذ والتاريخ</div>
                
                <div class="form-group">
                    <label class="form-label">تاريخ المسك:</label>
                    <input type="date" id="exam-date" class="form-input" />
                </div>

                <div class="form-group">
                    <label class="form-label">المادة الدراسية:</label>
                    <select id="subject-select" class="form-input">
                        <option value="">اختر المادة</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">اسم الأستاذ(ة):</label>
                    <select id="teacher-select" class="form-input">
                        <option value="">اختر الأستاذ</option>
                    </select>
                </div>
            </div>

            <!-- المرحلة الثانية: المستويات والأقسام -->
            <div id="stage2" class="stage">
                <div class="stage-title">المرحلة 2: اختيار المستويات والأقسام</div>
                
                <div class="instruction-text">
                    اختر المستوى ثم حدد الأقسام المطلوبة
                </div>

                <div class="level-navigator">
                    <button id="prev-level" class="nav-button">السابق</button>
                    <div id="current-level" class="level-card">المستوى</div>
                    <button id="next-level" class="nav-button">التالي</button>
                </div>

                <div id="sections-container" class="sections-grid">
                    <!-- الأقسام ستظهر هنا -->
                </div>

                <div class="selected-items">
                    <strong>الأقسام المختارة:</strong> <span id="selected-sections-display">لا توجد أقسام محددة</span>
                </div>
            </div>

            <!-- المرحلة الثالثة: نوع الفرض -->
            <div id="stage3" class="stage">
                <div class="stage-title">المرحلة 3: اختيار نوع الفرض</div>
                
                <table class="exam-types-table">
                    <thead>
                        <tr>
                            <th>تحديد</th>
                            <th>نوعه</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="exam-checkbox" data-exam="الفرض 1"></td>
                            <td>الفرض 1</td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="exam-checkbox" data-exam="الفرض 2"></td>
                            <td>الفرض 2</td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="exam-checkbox" data-exam="الفرض 3"></td>
                            <td>الفرض 3</td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="exam-checkbox" data-exam="الفرض 4"></td>
                            <td>الفرض 4</td>
                        </tr>
                    </tbody>
                </table>

                <div class="selected-items">
                    <strong>الفروض المختارة:</strong> <span id="selected-exams-display">لا توجد فروض محددة</span>
                </div>
            </div>
        </div>

        <!-- أزرار التنقل -->
        <div class="navigation-buttons">
            <button id="prev-btn" class="nav-btn">« السابق</button>
            <button id="next-btn" class="nav-btn">التالي »</button>
        </div>

        <!-- الأزرار الرئيسية -->
        <div class="action-buttons">
            <button id="save-btn" class="action-btn save-btn">مسك الفروض</button>
            <button id="cancel-btn" class="action-btn cancel-btn">إلغاء</button>
            <button id="delete-btn" class="action-btn delete-btn">حذف الفروض الممسوكة</button>
            <button id="section-report-btn" class="action-btn report-btn">تقرير القسم</button>
            <button id="teacher-report-btn" class="action-btn teacher-report-btn">تقرير الأستاذ</button>
        </div>

        <!-- رسائل النظام -->
        <div id="message-container"></div>

        <!-- شريط الحالة -->
        <div class="status-bar">
            جميع الحقوق محفوظة © 2025 - برنامج المعين في الحراسة العامة
        </div>
    </div>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        // متغيرات عامة
        let currentStage = 1;
        let levels = [];
        let currentLevelIndex = 0;
        let selectedSections = {};
        let selectedExams = [];
        let pyInterface = null;

        // تهيئة قناة الويب
        new QWebChannel(qt.webChannelTransport, function(channel) {
            pyInterface = channel.objects.pyInterface;
            initializeApp();
        });

        function initializeApp() {
            // تعيين التاريخ الحالي
            document.getElementById('exam-date').value = new Date().toISOString().split('T')[0];
            
            // تحميل البيانات الأولية
            loadSubjects();
            loadLevels();
            
            // ربط الأحداث
            bindEvents();
            
            // تحديث حالة الأزرار
            updateNavigationButtons();
        }

        function bindEvents() {
            // أحداث التنقل
            document.getElementById('prev-btn').addEventListener('click', goToPreviousStage);
            document.getElementById('next-btn').addEventListener('click', goToNextStage);
            
            // أحداث المادة والأستاذ
            document.getElementById('subject-select').addEventListener('change', onSubjectChange);
            
            // أحداث المستويات
            document.getElementById('prev-level').addEventListener('click', goToPreviousLevel);
            document.getElementById('next-level').addEventListener('click', goToNextLevel);
            
            // أحداث الفروض
            document.querySelectorAll('.exam-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', onExamTypeChange);
            });
            
            // أحداث الأزرار الرئيسية
            document.getElementById('save-btn').addEventListener('click', saveExams);
            document.getElementById('cancel-btn').addEventListener('click', resetForm);
            document.getElementById('delete-btn').addEventListener('click', () => pyInterface.showDeleteExamDialog());
            document.getElementById('section-report-btn').addEventListener('click', () => pyInterface.showSectionReport());
            document.getElementById('teacher-report-btn').addEventListener('click', () => pyInterface.showTeacherReport());
        }

        function loadSubjects() {
            pyInterface.loadSubjects('').then(response => {
                const subjects = JSON.parse(response);
                const select = document.getElementById('subject-select');
                select.innerHTML = '<option value="">اختر المادة</option>';
                subjects.forEach(subject => {
                    const option = document.createElement('option');
                    option.value = subject;
                    option.textContent = subject;
                    select.appendChild(option);
                });
            });
        }

        function onSubjectChange() {
            const subject = document.getElementById('subject-select').value;
            const teacherSelect = document.getElementById('teacher-select');
            teacherSelect.innerHTML = '<option value="">اختر الأستاذ</option>';
            
            if (subject) {
                pyInterface.loadTeachers(subject).then(response => {
                    const teachers = JSON.parse(response);
                    teachers.forEach(teacher => {
                        const option = document.createElement('option');
                        option.value = teacher;
                        option.textContent = teacher;
                        teacherSelect.appendChild(option);
                    });
                });
            }
        }

        function loadLevels() {
            pyInterface.loadLevels().then(response => {
                levels = JSON.parse(response);
                currentLevelIndex = 0;
                
                // تهيئة الأقسام المختارة
                levels.forEach(level => {
                    selectedSections[level] = [];
                });
                
                updateLevelDisplay();
            });
        }

        function updateLevelDisplay() {
            if (levels.length === 0) return;
            
            const currentLevel = levels[currentLevelIndex];
            document.getElementById('current-level').textContent = currentLevel;
            
            // تحديث حالة أزرار التنقل
            document.getElementById('prev-level').disabled = currentLevelIndex === 0;
            document.getElementById('next-level').disabled = currentLevelIndex === levels.length - 1;
            
            // تحميل أقسام المستوى الحالي
            loadSectionsForLevel(currentLevel);
        }

        function loadSectionsForLevel(level) {
            pyInterface.loadSections(level).then(response => {
                const sections = JSON.parse(response);
                displaySections(sections, level);
            });
        }

        function displaySections(sections, level) {
            const container = document.getElementById('sections-container');
            container.innerHTML = '';
            
            if (sections.length === 0) {
                container.innerHTML = '<div style="grid-column: 1/-1; text-align: center; font-weight: bold;">لا توجد أقسام</div>';
                return;
            }
            
            sections.forEach(section => {
                const sectionDiv = document.createElement('div');
                sectionDiv.className = 'section-card';
                sectionDiv.textContent = section;
                
                // التحقق من كون القسم محدد
                if (selectedSections[level] && selectedSections[level].includes(section)) {
                    sectionDiv.classList.add('selected');
                }
                
                sectionDiv.addEventListener('click', () => toggleSection(section, level));
                container.appendChild(sectionDiv);
            });
            
            updateSelectedSectionsDisplay();
        }

        function toggleSection(section, level) {
            if (!selectedSections[level]) {
                selectedSections[level] = [];
            }
            
            const index = selectedSections[level].indexOf(section);
            if (index > -1) {
                selectedSections[level].splice(index, 1);
            } else {
                selectedSections[level].push(section);
            }
            
            // تحديث العرض
            loadSectionsForLevel(level);
        }

        function updateSelectedSectionsDisplay() {
            const allSelected = [];
            Object.keys(selectedSections).forEach(level => {
                allSelected.push(...selectedSections[level]);
            });
            
            const display = document.getElementById('selected-sections-display');
            display.textContent = allSelected.length > 0 ? allSelected.join('، ') : 'لا توجد أقسام محددة';
        }

        function goToPreviousLevel() {
            if (currentLevelIndex > 0) {
                currentLevelIndex--;
                updateLevelDisplay();
            }
        }

        function goToNextLevel() {
            if (currentLevelIndex < levels.length - 1) {
                currentLevelIndex++;
                updateLevelDisplay();
            }
        }

        function onExamTypeChange() {
            selectedExams = [];
            document.querySelectorAll('.exam-checkbox:checked').forEach(checkbox => {
                selectedExams.push(checkbox.getAttribute('data-exam'));
            });
            
            const display = document.getElementById('selected-exams-display');
            display.textContent = selectedExams.length > 0 ? selectedExams.join('، ') : 'لا توجد فروض محددة';
        }

        function goToNextStage() {
            if (currentStage === 1) {
                // التحقق من البيانات الأساسية
                const subject = document.getElementById('subject-select').value;
                const teacher = document.getElementById('teacher-select').value;
                
                if (!subject || !teacher) {
                    showMessage('يرجى اختيار المادة والأستاذ أولاً.', 'error');
                    return;
                }
            } else if (currentStage === 2) {
                // التحقق من اختيار الأقسام
                const allSelected = [];
                Object.keys(selectedSections).forEach(level => {
                    allSelected.push(...selectedSections[level]);
                });
                
                if (allSelected.length === 0) {
                    showMessage('يرجى اختيار قسم واحد على الأقل.', 'error');
                    return;
                }
            }
            
            if (currentStage < 3) {
                currentStage++;
                updateStageDisplay();
                updateNavigationButtons();
            }
        }

        function goToPreviousStage() {
            if (currentStage > 1) {
                currentStage--;
                updateStageDisplay();
                updateNavigationButtons();
            }
        }

        function updateStageDisplay() {
            // إخفاء جميع المراحل
            document.querySelectorAll('.stage').forEach(stage => {
                stage.classList.remove('active');
            });
            
            // إظهار المرحلة الحالية
            document.getElementById(`stage${currentStage}`).classList.add('active');
        }

        function updateNavigationButtons() {
            document.getElementById('prev-btn').disabled = currentStage === 1;
            document.getElementById('next-btn').disabled = currentStage === 3;
            
            // إخفاء أزرار التنقل في المرحلة الأخيرة
            const navButtons = document.querySelector('.navigation-buttons');
            navButtons.style.display = currentStage === 3 ? 'none' : 'flex';
        }

        function saveExams() {
            // جمع البيانات
            const date = document.getElementById('exam-date').value;
            const subject = document.getElementById('subject-select').value;
            const teacher = document.getElementById('teacher-select').value;
            
            // جمع الأقسام المختارة
            const allSelectedSections = [];
            Object.keys(selectedSections).forEach(level => {
                allSelectedSections.push(...selectedSections[level]);
            });
            
            // التحقق من البيانات
            if (!date || !subject || !teacher || allSelectedSections.length === 0 || selectedExams.length === 0) {
                showMessage('يرجى إكمال جميع البيانات المطلوبة.', 'error');
                return;
            }
            
            // حفظ البيانات
            pyInterface.saveExams(
                date,
                subject,
                teacher,
                JSON.stringify(allSelectedSections),
                JSON.stringify(selectedExams)
            ).then(response => {
                const result = JSON.parse(response);
                if (result.success) {
                    showMessage(`${result.message}<br>المادة: ${result.details.subject}<br>الأستاذ: ${result.details.teacher}<br>الأقسام: ${result.details.sections.join('، ')}<br>الفروض: ${result.details.exams.join('، ')}`, 'success');
                    resetForm();
                } else {
                    showMessage(result.message, 'error');
                }
            });
        }

        function resetForm() {
            // إعادة تعيين النموذج
            currentStage = 1;
            currentLevelIndex = 0;
            selectedExams = [];
            
            // إعادة تعيين الأقسام المختارة
            Object.keys(selectedSections).forEach(level => {
                selectedSections[level] = [];
            });
            
            // إعادة تعيين الحقول
            document.getElementById('exam-date').value = new Date().toISOString().split('T')[0];
            document.getElementById('subject-select').value = '';
            document.getElementById('teacher-select').value = '';
            
            // إعادة تعيين مربعات الاختيار
            document.querySelectorAll('.exam-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            
            // تحديث العرض
            updateStageDisplay();
            updateNavigationButtons();
            updateSelectedSectionsDisplay();
            onExamTypeChange();
            onSubjectChange();
            updateLevelDisplay();
            
            // إزالة الرسائل
            document.getElementById('message-container').innerHTML = '';
        }

        function showMessage(message, type) {
            const container = document.getElementById('message-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = type === 'success' ? 'success-message' : 'error-message';
            messageDiv.innerHTML = message;
            
            container.innerHTML = '';
            container.appendChild(messageDiv);
            
            // إزالة الرسالة بعد 5 ثوان
            setTimeout(() => {
                if (container.contains(messageDiv)) {
                    container.removeChild(messageDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>
        """

    def show_teacher_exams_report(self):
        """عرض تقرير الفروض حسب الأستاذ وإنشاء ملف PDF"""
        if not REPORTLAB_AVAILABLE:
            ConfirmationDialogs.show_custom_error_message(self, "مكتبة ReportLab غير متاحة. لا يمكن إنشاء التقرير.", "خطأ")
            return

        try:
            # إنشاء نافذة حوار لاختيار الأستاذ
            teacher_dialog = QDialog(self)
            teacher_dialog.setWindowTitle("اختيار الأستاذ للتقرير")
            teacher_dialog.setMinimumWidth(400)
            teacher_dialog.setLayoutDirection(Qt.RightToLeft)

            dialog_layout = QVBoxLayout(teacher_dialog)

            teacher_label = QLabel("اختر الأستاذ:")
            teacher_label.setFont(QFont("Calibri", 13, QFont.Bold))

            teacher_combo = QComboBox()
            teacher_combo.setFont(QFont("Calibri", 13))

            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT الأستاذ FROM مسك_أوراق_الفروض
                WHERE الأستاذ IS NOT NULL AND الأستاذ != ''
                ORDER BY الأستاذ
            """)
            teachers = [row[0] for row in cursor.fetchall()]
            conn.close()

            if not teachers:
                ConfirmationDialogs.show_custom_info_message(self, "لا توجد بيانات فروض مسجلة للأساتذة لإنشاء تقرير.", "معلومات")
                return

            teacher_combo.addItems(teachers)
            dialog_layout.addWidget(teacher_label)
            dialog_layout.addWidget(teacher_combo)

            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.button(QDialogButtonBox.Ok).setText("إنشاء التقرير")
            button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
            button_box.accepted.connect(teacher_dialog.accept)
            button_box.rejected.connect(teacher_dialog.reject)
            dialog_layout.addWidget(button_box)

            if teacher_dialog.exec_() != QDialog.Accepted:
                return

            selected_teacher = teacher_combo.currentText()
            if not selected_teacher:
                return

            # الحصول على السنة الدراسية الحالية
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            current_year = result[0] if result else ""

            # جلب بيانات الفروض للأستاذ المحدد
            cursor.execute("""
                SELECT المادة, المستوى, القسم, نوع_الفرض, التاريخ, الأسدس
                FROM مسك_أوراق_الفروض
                WHERE الأستاذ = ? AND السنة_الدراسية = ?
                ORDER BY التاريخ DESC, المادة, المستوى, القسم, نوع_الفرض
            """, (selected_teacher, current_year))
            exams_data = cursor.fetchall()
            conn.close()

            if not exams_data:
                ConfirmationDialogs.show_custom_info_message(self, f"لا توجد فروض مسجلة للأستاذ {selected_teacher} في السنة الدراسية الحالية.", "معلومات")
                return

            # جلب اسم المؤسسة
            school_name_str = "اسم المؤسسة (يرجى التحقق)"
            try:
                conn = sqlite3.connect("data.db")
                cursor = conn.cursor()
                cursor.execute("SELECT اسم_المؤسسة_الرسمي FROM بيانات_المؤسسة LIMIT 1")
                school_name_db_result = cursor.fetchone()
                if school_name_db_result and school_name_db_result[0]:
                    school_name_str = school_name_db_result[0]
                conn.close()
            except sqlite3.Error as db_err:
                print(f"خطأ في جلب اسم المؤسسة: {db_err}")

            # إنشاء مسار لحفظ التقرير
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            reports_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة", "الفروض الممسوكة")

            if not os.path.exists(reports_folder):
                os.makedirs(reports_folder)

            current_date = datetime.now().strftime("%Y-%m-%d")
            file_name = f"تقرير_{selected_teacher}_{exams_data[0][0]}_{current_date}.pdf"
            pdf_filename = os.path.join(reports_folder, file_name)

            # إنشاء التقرير
            report_generated = generate_teacher_pdf_report_code(
                selected_teacher,
                exams_data,
                pdf_filename,
                current_year,
                school_name_str,
                "#1976D2"
            )

            if report_generated:
                success_message = f"""تم إنشاء تقرير الأستاذ بنجاح
الأستاذ: {selected_teacher}
المادة: {exams_data[0][0]}
مسار الملف: {pdf_filename}"""

                ConfirmationDialogs.show_custom_success_message(self, success_message, "نجاح إنشاء التقرير")

                try:
                    os.startfile(reports_folder)
                except Exception as e_open:
                    print(f"لم يتمكن من فتح المجلد: {e_open}")
            else:
                ConfirmationDialogs.show_custom_error_message(self, "فشل إنشاء تقرير PDF.", "خطأ في إنشاء التقرير")

        except Exception as e:
            error_message = f"حدث خطأ أثناء معالجة طلب تقرير الأستاذ:\n{str(e)}"
            ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ")
            traceback.print_exc()

    def show_section_exams_report(self):
        """عرض تقرير الفروض حسب القسم"""
        try:
            # إنشاء نافذة حوار لاختيار المستوى والقسم
            section_dialog = QDialog(self)
            section_dialog.setWindowTitle("اختيار القسم للتقرير")
            section_dialog.setMinimumWidth(400)
            section_dialog.setLayoutDirection(Qt.RightToLeft)

            dialog_layout = QVBoxLayout(section_dialog)

            level_label = QLabel("اختر المستوى:")
            level_label.setFont(QFont("Calibri", 13, QFont.Bold))

            level_combo = QComboBox()
            level_combo.setFont(QFont("Calibri", 13))

            section_label = QLabel("اختر القسم:")
            section_label.setFont(QFont("Calibri", 13, QFont.Bold))

            section_combo = QComboBox()
            section_combo.setFont(QFont("Calibri", 13))

            # تحميل المستويات
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT المستوى FROM مسك_أوراق_الفروض
                WHERE المستوى IS NOT NULL AND المستوى != ''
                ORDER BY المستوى
            """)
            levels = [row[0] for row in cursor.fetchall()]

            if not levels:
                ConfirmationDialogs.show_custom_info_message(self, "لا توجد بيانات فروض مسجلة للأقسام لإنشاء تقرير.", "معلومات")
                return

            level_combo.addItems(levels)

            def update_sections():
                selected_level = level_combo.currentText()
                section_combo.clear()

                cursor.execute("""
                    SELECT DISTINCT القسم FROM مسك_أوراق_الفروض
                    WHERE المستوى = ? AND القسم IS NOT NULL AND القسم != ''
                    ORDER BY القسم
                """, (selected_level,))

                sections = [row[0] for row in cursor.fetchall()]
                section_combo.addItems(sections)

            level_combo.currentIndexChanged.connect(update_sections)
            update_sections()

            dialog_layout.addWidget(level_label)
            dialog_layout.addWidget(level_combo)
            dialog_layout.addWidget(section_label)
            dialog_layout.addWidget(section_combo)

            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.button(QDialogButtonBox.Ok).setText("إنشاء التقرير")
            button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
            button_box.accepted.connect(section_dialog.accept)
            button_box.rejected.connect(section_dialog.reject)
            dialog_layout.addWidget(button_box)

            if section_dialog.exec_() != QDialog.Accepted:
                conn.close()
                return

            selected_level = level_combo.currentText()
            selected_section = section_combo.currentText()

            if not selected_level or not selected_section:
                ConfirmationDialogs.show_custom_warning_message(self, "يرجى اختيار المستوى والقسم أولاً.", "بيانات غير مكتملة")
                conn.close()
                return

            # الحصول على السنة الدراسية الحالية
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            current_year = result[0] if result else ""

            # جلب بيانات الفروض للقسم المحدد
            cursor.execute("""
                SELECT المادة, الأستاذ, نوع_الفرض, التاريخ, الأسدس
                FROM مسك_أوراق_الفروض
                WHERE المستوى = ? AND القسم = ? AND السنة_الدراسية = ?
                ORDER BY التاريخ DESC, المادة, الأستاذ
            """, (selected_level, selected_section, current_year))

            exams_data = cursor.fetchall()
            conn.close()

            if not exams_data:
                ConfirmationDialogs.show_custom_info_message(self, f"لا توجد فروض مسجلة للقسم {selected_section} في المستوى {selected_level} للسنة الدراسية الحالية.", "معلومات")
                return

            # جلب اسم المؤسسة
            school_name_str = "اسم المؤسسة (يرجى التحقق)"
            try:
                conn = sqlite3.connect("data.db")
                cursor = conn.cursor()
                cursor.execute("SELECT اسم_المؤسسة_الرسمي FROM بيانات_المؤسسة LIMIT 1")
                school_name_db_result = cursor.fetchone()
                if school_name_db_result and school_name_db_result[0]:
                    school_name_str = school_name_db_result[0]
                conn.close()
            except sqlite3.Error as db_err:
                print(f"خطأ في جلب اسم المؤسسة: {db_err}")

            # إنشاء مسار لحفظ التقرير
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            reports_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة", "الفروض الممسوكة")

            if not os.path.exists(reports_folder):
                os.makedirs(reports_folder)

            current_date = datetime.now().strftime("%Y-%m-%d")
            file_name = f"تقرير_قسم_{selected_level}_{selected_section}_{current_date}.pdf"
            pdf_filename = os.path.join(reports_folder, file_name)

            # إنشاء التقرير
            report_generated = generate_section_pdf_report(
                selected_level,
                selected_section,
                exams_data,
                pdf_filename,
                current_year,
                school_name_str,
                "#1976D2"
            )

            if report_generated:
                success_message = f"""تم إنشاء تقرير القسم بنجاح
المستوى: {selected_level}
القسم: {selected_section}
عدد الفروض: {len(exams_data)}
مسار الملف: {pdf_filename}"""

                ConfirmationDialogs.show_custom_success_message(self, success_message, "نجاح إنشاء تقرير القسم")

                try:
                    os.startfile(reports_folder)
                except Exception as e_open:
                    print(f"لم يتمكن من فتح المجلد: {e_open}")
            else:
                ConfirmationDialogs.show_custom_error_message(self, "فشل إنشاء تقرير PDF.", "خطأ في إنشاء التقرير")

        except Exception as e:
            error_message = f"حدث خطأ أثناء معالجة طلب تقرير القسم:\n{str(e)}"
            ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ عام")
            traceback.print_exc()

# إضافة نقطة دخول للاختبار المباشر
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CreateAbsenceTableWindowHTML()
    window.show()
    sys.exit(app.exec_())
