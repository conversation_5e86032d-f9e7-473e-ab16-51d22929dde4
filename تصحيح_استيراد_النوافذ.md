# تصحيح استيراد النوافذ في main_window.py

## 🔍 المشكلة المكتشفة:

عند تشغيل البرنامج، ظهرت الأخطاء التالية:

```
ERROR: خطأ في استيراد نافذة سجلات مسك تبرير الغياب: cannot import name 'AbsenceJustificationRecordsWindow' from 'sub16_window_html'
ERROR: خطأ في استيراد نافذة سجلات زيارة أولياء الأمور: cannot import name 'ParentVisitRecordsWindow' from 'sub17_window_html'
```

## 🔧 السبب:

الكلاسات في الملفين مختلفة عما توقعناه:

### الملفات الفعلية:
- **`sub16_window_html.py`** يحتوي على: `ParentVisitViewerWindow` (زيارة أولياء الأمور)
- **`sub17_window_html.py`** يحتوي على: `AbsenceJustificationViewerWindow` (تبريرات الغياب)

### ما كنا نتوقعه:
- **`sub16_window_html.py`** ← `AbsenceJustificationRecordsWindow`
- **`sub17_window_html.py`** ← `ParentVisitRecordsWindow`

## ✅ التصحيحات المطبقة:

### 1. تصحيح استيراد نافذة سجلات مسك تبرير الغياب:

**قبل التصحيح:**
```python
from sub16_window_html import AbsenceJustificationRecordsWindow
self.absence_justification_records_window = AbsenceJustificationRecordsWindow(parent=None)
```

**بعد التصحيح:**
```python
from sub17_window_html import AbsenceJustificationViewerWindow
self.absence_justification_records_window = AbsenceJustificationViewerWindow(parent=None)
```

### 2. تصحيح استيراد نافذة سجلات زيارة أولياء الأمور:

**قبل التصحيح:**
```python
from sub17_window_html import ParentVisitRecordsWindow
self.parent_visit_records_window = ParentVisitRecordsWindow(parent=None)
```

**بعد التصحيح:**
```python
from sub16_window_html import ParentVisitViewerWindow
self.parent_visit_records_window = ParentVisitViewerWindow(parent=None)
```

### 3. تصحيح رسائل الخطأ:

تم تحديث رسائل الخطأ لتشير إلى الملفات الصحيحة:
- رسالة خطأ تبريرات الغياب ← تشير إلى `sub17_window_html.py`
- رسالة خطأ زيارة أولياء الأمور ← تشير إلى `sub16_window_html.py`

## 📋 الملخص النهائي:

### الآن الأزرار تعمل كالتالي:

1. **📝 سجلات مسك تبرير الغياب** 
   - يستورد من: `sub17_window_html.py`
   - الكلاس: `AbsenceJustificationViewerWindow`
   - يفتح في كامل الشاشة ✅

2. **👨‍👩‍👧 سجلات زيارة أولياء الأمور**
   - يستورد من: `sub16_window_html.py`
   - الكلاس: `ParentVisitViewerWindow`
   - يفتح في كامل الشاشة ✅

## 🎯 النتيجة:

✅ **تم حل جميع أخطاء الاستيراد**
✅ **الأزرار تعمل الآن بشكل صحيح**
✅ **النوافذ تفتح في كامل الشاشة كما هو مطلوب**

---

## 🧪 للاختبار:

1. شغل البرنامج: `python main_window.py`
2. اضغط على تبويب "سجلات التلاميذ"
3. اختبر الأزرار:
   - **📝 سجلات مسك تبرير الغياب** ← يجب أن يفتح نافذة تبريرات الغياب
   - **👨‍👩‍👧 سجلات زيارة أولياء الأمور** ← يجب أن يفتح نافذة زيارة أولياء الأمور

✅ **التصحيحات مكتملة وجاهزة للاستخدام!**
