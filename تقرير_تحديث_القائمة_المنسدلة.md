# تقرير تحديث القائمة المنسدلة - سجلات التلاميذ

## ملخص التغييرات المطلوبة والمنجزة

### 📋 المطلوب:
1. **تغيير زر "👤 بطاقات التلاميذ"** إلى **"📝 سجلات مسك تبرير الغياب"**
   - يفتح نافذة `sub16_window_html.py` في كامل الشاشة

2. **تغيير زر "📊 تقارير الحضور"** إلى **"👨‍👩‍👧 سجلات زيارة أولياء الأمور"**
   - يفتح نافذة `sub17_window_html.py` في كامل الشاشة

---

## ✅ التغييرات المنجزة:

### 1. تحديث عناصر القائمة المنسدلة
**الملف:** `main_window.py` - السطور 516-524

**قبل التحديث:**
```python
self.student_records_dropdown_items = [
    ("⚠️ سجلات المخالفات", "violations_records", "#00BFFF"),
    ("📋 سجلات الدخول والتأخر", "entry_late_records", "#00BFFF"),
    ("👤 بطاقات التلاميذ", "student_cards", "#00BFFF"),           # ← تم تغييره
    ("📊 تقارير الحضور", "attendance_reports", "#00BFFF"),        # ← تم تغييره
    ("📝 سجلات الغياب", "absence_records", "#00BFFF"),
    ("🏥 زيارات الطبيب", "doctor_visits", "#00BFFF")
]
```

**بعد التحديث:**
```python
self.student_records_dropdown_items = [
    ("⚠️ سجلات المخالفات", "violations_records", "#00BFFF"),
    ("📋 سجلات الدخول والتأخر", "entry_late_records", "#00BFFF"),
    ("📝 سجلات مسك تبرير الغياب", "absence_justification_records", "#00BFFF"),  # ← جديد
    ("👨‍👩‍👧 سجلات زيارة أولياء الأمور", "parent_visit_records", "#00BFFF"),      # ← جديد
    ("📝 سجلات الغياب", "absence_records", "#00BFFF"),
    ("🏥 زيارات الطبيب", "doctor_visits", "#00BFFF")
]
```

### 2. إضافة دالة فتح نافذة سجلات مسك تبرير الغياب
**الملف:** `main_window.py` - السطور 699-738

```python
def _open_absence_justification_records_window(self):
    """فتح نافذة سجلات مسك تبرير الغياب في كامل الشاشة"""
    try:
        # استيراد النافذة
        from sub16_window_html import AbsenceJustificationRecordsWindow
        
        # إنشاء النافذة
        self.absence_justification_records_window = AbsenceJustificationRecordsWindow(
            parent=None  # بدون parent لضمان فتحها في كامل الشاشة
        )
        
        # فتح النافذة في كامل الشاشة
        self.absence_justification_records_window.show()
        self.absence_justification_records_window.showMaximized()
        
    except ImportError as e:
        # معالجة خطأ الاستيراد
        QMessageBox.critical(self, "خطأ في الاستيراد", 
                           f"تأكد من وجود ملف sub16_window_html.py")
```

### 3. إضافة دالة فتح نافذة سجلات زيارة أولياء الأمور
**الملف:** `main_window.py` - السطور 740-779

```python
def _open_parent_visit_records_window(self):
    """فتح نافذة سجلات زيارة أولياء الأمور في كامل الشاشة"""
    try:
        # استيراد النافذة
        from sub17_window_html import ParentVisitRecordsWindow
        
        # إنشاء النافذة
        self.parent_visit_records_window = ParentVisitRecordsWindow(
            parent=None  # بدون parent لضمان فتحها في كامل الشاشة
        )
        
        # فتح النافذة في كامل الشاشة
        self.parent_visit_records_window.show()
        self.parent_visit_records_window.showMaximized()
        
    except ImportError as e:
        # معالجة خطأ الاستيراد
        QMessageBox.critical(self, "خطأ في الاستيراد", 
                           f"تأكد من وجود ملف sub17_window_html.py")
```

### 4. تحديث دالة التنقل
**الملف:** `main_window.py` - السطور 807-822

```python
# التعامل مع النوافذ المختلفة
if window_key == "entry_late_records":
    self._open_entry_late_records_window()
elif window_key == "violations_records":
    self._open_violations_window()
elif window_key == "absence_justification_records":          # ← جديد
    self._open_absence_justification_records_window()
elif window_key == "parent_visit_records":                   # ← جديد
    self._open_parent_visit_records_window()
else:
    # النوافذ الأخرى قيد التطوير
    QMessageBox.information(self, "قيد التطوير", 
                          f"سيتم إضافة نافذة {window_key} قريباً")
```

---

## 🔧 الميزات المضافة:

### ✅ إدارة النوافذ المحسنة:
- **إغلاق النافذة السابقة** تلقائياً قبل فتح نافذة جديدة
- **ربط إشارة الإغلاق** لتنظيف المراجع
- **فتح في كامل الشاشة** باستخدام `showMaximized()`

### ✅ معالجة الأخطاء:
- **معالجة أخطاء الاستيراد** مع رسائل واضحة
- **معالجة الأخطاء العامة** مع تفاصيل الخطأ
- **رسائل تأكيد** لإرشاد المستخدم

### ✅ التوافق:
- **متوافق مع البنية الحالية** للبرنامج
- **يحافظ على النمط المتبع** في الدوال الأخرى
- **لا يؤثر على الوظائف الموجودة**

---

## 📁 الملفات المطلوبة:

لكي تعمل التحديثات بشكل كامل، يجب التأكد من وجود:

1. **`sub16_window_html.py`** - نافذة سجلات مسك تبرير الغياب
   - يجب أن تحتوي على كلاس `AbsenceJustificationRecordsWindow`

2. **`sub17_window_html.py`** - نافذة سجلات زيارة أولياء الأمور
   - يجب أن تحتوي على كلاس `ParentVisitRecordsWindow`

---

## 🧪 ملف الاختبار:

تم إنشاء ملف `test_main_window_updates.py` للتحقق من:
- ✅ استيراد النافذة الرئيسية
- ✅ استيراد النوافذ الجديدة
- ✅ تحديث عناصر القائمة المنسدلة
- ✅ وجود الدوال الجديدة

---

## 🎯 النتيجة النهائية:

عند النقر على الأزرار في القائمة المنسدلة "سجلات التلاميذ":

1. **📝 سجلات مسك تبرير الغياب** ← يفتح `sub16_window_html.py` في كامل الشاشة
2. **👨‍👩‍👧 سجلات زيارة أولياء الأمور** ← يفتح `sub17_window_html.py` في كامل الشاشة

✅ **تم إنجاز جميع المتطلبات بنجاح!**
